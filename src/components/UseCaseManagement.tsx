import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Alert, AlertDescription } from './ui/alert';
import { Badge } from './ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { useToast } from '../hooks/use-toast';
import { searchUseCases, uploadUseCaseData, jsonToCsv, downloadFile, getKnowledgeBaseDocuments, getDocumentSegments, searchAllDocumentSegments, deleteDocument, deleteDocumentSegment, createDocumentSegments } from '../services/api';
import { UseCaseItem, KnowledgeBase, DocumentItem, Pagination, DocumentSegment, DocumentSegmentsResponse } from '../types';
import { FileText, Search, Plus, Trash2, Download, Upload, AlertCircle, CheckCircle2, RefreshCw, BookOpen, Eye } from 'lucide-react';

export default function UseCaseManagement() {
  const location = useLocation();
  const knowledgeBase = location.state?.knowledgeBase as KnowledgeBase;
  const defaultTab = location.state?.defaultTab || 'search';

  // 添加在现有状态变量之后
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [submitSuccess, setSubmitSuccess] = useState(false);

  // 在组件顶部，状态声明之后
  const dbTypes = [
    "TDSQL-PG",
    "TDSQL2.5",
    "TDSQL3",
    "CynosDB",
    "TDSQL-Oracle"
  ];

  
  const [useCases, setUseCases] = useState<UseCaseItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [deleteTarget, setDeleteTarget] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [useCaseRows, setUseCaseRows] = useState([
    {
      id: '',
      name: '',
      description: '',
      type: '',
      db_type: '',
      content: ['']
    }
  ]);
  const [fileName, setFileName] = useState('');
  const [documents, setDocuments] = useState<DocumentItem[]>([]);
  const [pagination, setPagination] = useState<Pagination>({ page: 1, limit: 20, total: 0 });
  const [loadingDocuments, setLoadingDocuments] = useState(false);
  const [documentSegments, setDocumentSegments] = useState<DocumentSegment[]>([]);
  const [loadingSegments, setLoadingSegments] = useState(false);
  const [selectedDocumentId, setSelectedDocumentId] = useState('');
  const [searchMode, setSearchMode] = useState<'keyword' | 'document'>('keyword');
  const [showSegments, setShowSegments] = useState(false);
  const [showRawSegments, setShowRawSegments] = useState(false); // 新增：显示原始分段数据
  const { toast } = useToast();

  useEffect(() => {
    if (knowledgeBase) {
      fetchDocuments();
    }
  }, [knowledgeBase]);

  // 获取知识库文档列表
  const fetchDocuments = async (page = 1, limit = 20) => {
    setLoadingDocuments(true);
    try {
      const response = await getKnowledgeBaseDocuments(knowledgeBase.id, { page, limit });
      
      if (response.success && response.data) {
        setDocuments(response.data.data);
        setPagination(response.data.pagination);
      } else {
        toast({
          title: "获取文档列表失败",
          description: response.error || "无法加载文档列表",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "获取文档列表异常",
        description: "网络连接异常,请稍后重试",
        variant: "destructive",
      });
    } finally {
      setLoadingDocuments(false);
    }
  };

  const handlePageChange = (newPage: number) => {
    fetchDocuments(newPage, pagination.limit);
  };

  const handleLimitChange = (newLimit: number) => {
    fetchDocuments(1, newLimit);
  };

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return <Badge variant="secondary">可用</Badge>;
      case 'pending':
        return <Badge variant="secondary">处理中</Badge>;
      case 'error':
        return <Badge variant="destructive">错误</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (timestamp: string | number) => {
    const ts = typeof timestamp === 'string' ? parseInt(timestamp, 10) : timestamp;
    const isSeconds = ts < 9999999999;
    const date = new Date(isSeconds ? ts * 1000 : ts);
    
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 检查输入是否为文档ID（UUID格式）
  const isDocumentId = (input: string): boolean => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(input.trim());
  };

  // 根据文件名查找匹配的文档
  const findDocumentByName = (fileName: string): DocumentItem | null => {
    if (!fileName.trim()) return null;

    // 移除文件扩展名进行匹配
    const nameWithoutExt = fileName.replace(/\.(csv|txt)$/i, '').trim();

    // 在文档列表中查找名称匹配的文档
    const matchedDoc = documents.find(doc => {
      const docNameWithoutExt = doc.name.replace(/\.(csv|txt)$/i, '').trim();
      return (
        doc.name.toLowerCase() === nameWithoutExt.toLowerCase() ||
        doc.name.toLowerCase() === fileName.toLowerCase() ||
        docNameWithoutExt.toLowerCase() === nameWithoutExt.toLowerCase() ||
        docNameWithoutExt.toLowerCase() === fileName.toLowerCase()
      );
    });

    return matchedDoc || null;
  };

  // 预处理用例内容以确保作为单个分段
  const preprocessUseCaseContent = (content: string): string => {
    // 为用例内容添加明确的标识，确保被识别为单个分段
    let processed = content;

    // 确保内容以明确的标识开始
    if (!processed.startsWith('-- 用例数据开始')) {
      processed = '-- 用例数据开始\n' + processed;
    }

    // 确保内容以明确的标识结束
    if (!processed.endsWith('-- 用例数据结束')) {
      processed = processed + '\n-- 用例数据结束';
    }

    console.log('用例内容预处理:', {
      original: content.substring(0, 100) + '...',
      processed: processed.substring(0, 100) + '...'
    });

    return processed;
  };

  // 获取文档分段
  const fetchDocumentSegments = async (documentId: string, query?: string) => {
    setLoadingSegments(true);
    try {
      const response = await getDocumentSegments(knowledgeBase.id, documentId, {
        query: query,
        page: 1,
        limit: 100
      });
      
      if (response.success && response.data) {
        setDocumentSegments(response.data.data);
        
        // 解析分段内容为用例数据
        const parsedUseCases = parseSegmentToUseCases(response.data.data);
        setUseCases(parsedUseCases);
        
        setSelectedDocumentId(documentId);
        setShowSegments(false); // 不显示原始分段，而是显示解析后的用例表格
        
        toast({
          title: "获取文档内容成功",
          description: `找到 ${parsedUseCases.length} 条用例记录`,
        });
      } else {
        toast({
          title: "获取文档内容失败",
          description: response.error || "无法获取文档分段",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "获取文档内容错误",
        description: "网络连接错误，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setLoadingSegments(false);
    }
  };

  // 搜索所有文档
  const searchAllDocuments = async (query: string) => {
    setLoadingSegments(true);
    try {
      const documentIds = documents.map(doc => doc.id);
      
      if (documentIds.length === 0) {
        toast({
          title: "搜索失败",
          description: "当前知识库中没有文档",
          variant: "destructive",
        });
        return;
      }
      
      const response = await searchAllDocumentSegments(knowledgeBase.id, query, documentIds);
      
      if (response.success && response.data) {
        // 合并所有文档的分段结果（现在已经在API层过滤过了）
        const allSegments: DocumentSegment[] = [];
        response.data.forEach(docResponse => {
          allSegments.push(...docResponse.data);
        });
        
        // 解析分段内容为用例数据
        const parsedUseCases = parseSegmentToUseCases(allSegments);
        setUseCases(parsedUseCases);
        
        // 同时保留原始分段数据供调试
        setDocumentSegments(allSegments);
        setSelectedDocumentId('');
        setShowSegments(false); // 不显示原始分段，而是显示解析后的用例表格
        
        if (parsedUseCases.length > 0) {
          toast({
            title: "搜索完成",
            description: `在 ${response.data.length} 个文档中找到 ${parsedUseCases.length} 条用例记录`,
          });
        } else {
          toast({
            title: "搜索完成",
            description: `未找到包含 "${query}" 的相关用例`,
            variant: "destructive",
          });
        }
      } else {
        toast({
          title: "搜索失败",
          description: response.error || "搜索过程中发生错误",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "搜索错误",
        description: "网络连接错误，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setLoadingSegments(false);
    }
  };

  // 解析文档分段内容为用例数据
  const parseSegmentToUseCases = (segments: DocumentSegment[]): UseCaseItem[] => {
    const parsedUseCases: UseCaseItem[] = [];
    
    segments.forEach((segment, segmentIndex) => {
      try {
        // 尝试解析分段内容，假设格式为结构化文本
        const content = segment.content;
        
        // 简单的解析逻辑，根据你提供的格式
        // 格式: id: 24;name: partition table rule;description: 分区表测试;type: rule;db_type: TDSQL2.5;content: ['创建两张分区表,分区类型相同']
        
        const lines = content.split('\n').filter(line => line.trim());
        
        let hasValidParsedContent = false;
        
        for (const line of lines) {
          // 尝试匹配包含用例信息的行
          const useCaseMatch = line.match(/id:\s*([^;]+);\s*name:\s*([^;]+);\s*description:\s*([^;]+);\s*type:\s*([^;]+);\s*db_type:\s*([^;]+);\s*content:\s*(.+)/);
          
          if (useCaseMatch) {
            const [, id, name, description, type, db_type, contentStr] = useCaseMatch;
            
            // 解析content数组
            let contentArray: string[] = [];
            try {
              // 尝试解析类似 ['创建两张分区表,分区类型相同'] 的格式
              const cleanContent = contentStr.trim();
              if (cleanContent.startsWith('[') && cleanContent.endsWith(']')) {
                const arrayContent = cleanContent.slice(1, -1);
                // 简单分割，假设用逗号分隔
                contentArray = arrayContent.split(',').map(item => 
                  item.trim().replace(/^['"]|['"]$/g, '')
                ).filter(item => item.length > 0);
              } else {
                contentArray = [cleanContent.replace(/^['"]|['"]$/g, '')];
              }
            } catch (e) {
              contentArray = [contentStr.trim()];
            }
            
            const useCase: UseCaseItem = {
              id: id.trim(),
              name: name.trim(),
              description: description.trim(),
              type: type.trim(),
              db_type: db_type.trim(),
              content: contentArray,
              document_id: segment.document_id
            };
            
            parsedUseCases.push(useCase);
            hasValidParsedContent = true;
          }
        }
        
        // 只有在没有解析出任何有效内容时才添加fallback
        if (!hasValidParsedContent && content.trim()) {
          const fallbackUseCase: UseCaseItem = {
            id: `seg-${segment.position}`,
            name: `分段-${segment.position}`,
            description: segment.answer || '从文档分段解析',
            type: segment.status || '未知',
            db_type: segment.keywords.join(', ') || '未知',
            content: [content.trim()],
            document_id: segment.document_id
          };
          
          parsedUseCases.push(fallbackUseCase);
        }
        
      } catch (error) {
        console.error('解析分段内容失败:', error, segment);
        
        // 出错时创建备用用例
        const errorUseCase: UseCaseItem = {
          id: `err-${segment.position}`,
          name: `分段-${segment.position}`,
          description: segment.answer || '解析失败',
          type: '解析错误',
          db_type: '未知',
          content: [segment.content],
          document_id: segment.document_id
        };
        
        parsedUseCases.push(errorUseCase);
      }
    });
    
    return parsedUseCases;
  };

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      toast({
        title: "搜索提示",
        description: "请输入搜索关键词或文档ID",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    setUseCases([]); // 清空用例搜索结果
    
    try {
      // 判断输入的是文档ID还是关键词
      if (isDocumentId(searchTerm)) {
        setSearchMode('document');
        await fetchDocumentSegments(searchTerm.trim());
      } else {
        setSearchMode('keyword');
        // 先搜索传统用例
        const useCaseResponse = await searchUseCases({
          query: searchTerm,
          knowledge_base_id: knowledgeBase?.id
        });
        
        if (useCaseResponse.success && useCaseResponse.data) {
          setUseCases(useCaseResponse.data);
        }
        
        // 然后搜索文档分段
        await searchAllDocuments(searchTerm);
      }
    } catch (error) {
      toast({
        title: "搜索错误",
        description: "网络连接错误，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 删除功能
  const handleDelete = async () => {
    if (!deleteTarget.trim()) {
      toast({
        title: "删除提示",
        description: "请输入要删除的文档ID或关键词",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      // 判断输入的是文档ID还是关键词
      if (isDocumentId(deleteTarget)) {
        // 删除整个文档
        const response = await deleteDocument(knowledgeBase.id, deleteTarget.trim());

        if (response.success) {
          toast({
            title: "删除成功",
            description: "文档已成功删除",
          });

          // 刷新文档列表
          await fetchDocuments();

          // 清空搜索结果
          setUseCases([]);
          setDocumentSegments([]);
          setDeleteTarget('');
        } else {
          toast({
            title: "删除失败",
            description: response.error || "删除文档时发生错误",
            variant: "destructive",
          });
        }
      } else {
        // 根据关键词搜索并删除匹配的分段
        // 首先搜索所有文档的分段
        const documentIds = documents.map(doc => doc.id);
        const searchResponse = await searchAllDocumentSegments(knowledgeBase.id, deleteTarget, documentIds);

        if (searchResponse.success && searchResponse.data) {
          let deletedCount = 0;
          let totalSegments = 0;

          // 统计总分段数
          searchResponse.data.forEach(docSegments => {
            totalSegments += docSegments.data.length;
          });

          if (totalSegments === 0) {
            toast({
              title: "未找到匹配项",
              description: "没有找到包含该关键词的分段",
              variant: "destructive",
            });
            return;
          }

          // 删除所有匹配的分段
          for (const docSegments of searchResponse.data) {
            for (const segment of docSegments.data) {
              try {
                const deleteResponse = await deleteDocumentSegment(
                  knowledgeBase.id,
                  segment.document_id,
                  segment.id
                );

                if (deleteResponse.success) {
                  deletedCount++;
                }
              } catch (error) {
                console.error(`删除分段 ${segment.id} 失败:`, error);
              }
            }
          }

          toast({
            title: "删除完成",
            description: `成功删除 ${deletedCount}/${totalSegments} 个匹配的分段`,
          });

          // 清空输入并刷新文档列表
          setDeleteTarget('');
          await fetchDocuments();
        } else {
          toast({
            title: "搜索失败",
            description: "无法搜索匹配的分段",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      toast({
        title: "删除错误",
        description: "删除过程中发生错误，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddUseCases = async () => {
    try {
      // 重置错误状态
      setValidationErrors({});
      setSubmitSuccess(false);

      // 验证表格数据
      const validRows = useCaseRows.filter(row =>
        row.id.trim() &&
        row.name.trim() &&
        row.description.trim() &&
        row.type.trim() &&
        row.db_type.trim() &&
        row.content.some(c => c.trim())
      );

      if (validRows.length === 0) {
        throw new Error('请至少填写一行完整的用例数据');
      }

      // 验证数据完整性
      const errors: Record<string, string> = {};
      useCaseRows.forEach((row, index) => {
        if (!row.id.trim()) errors[`id-${index}`] = "ID不能为空";
        if (!row.name.trim()) errors[`name-${index}`] = "名称不能为空";
        if (!row.description.trim()) errors[`description-${index}`] = "描述不能为空";
        if (!row.type.trim()) errors[`type-${index}`] = "类型不能为空";
        if (!row.db_type.trim()) errors[`db_type-${index}`] = "数据库类型不能为空";
        if (!row.content.some(c => c.trim())) errors[`content-${index}`] = "内容不能为空";
      });

      if (Object.keys(errors).length > 0) {
        setValidationErrors(errors);
        throw new Error('请填写所有必填字段');
      }

      setIsUploading(true);

      // 处理用例内容，过滤空值
      const processedData = validRows.map(row => ({
        ...row,
        content: row.content.filter(c => c.trim())
      }));

      // 检查是否有匹配的文档
      const matchedDocument = fileName.trim() ? findDocumentByName(fileName.trim()) : null;

      console.log('调试信息:', {
        fileName: fileName.trim(),
        documentsCount: documents.length,
        documentNames: documents.map(doc => doc.name),
        matchedDocument: matchedDocument
      });

      if (matchedDocument && knowledgeBase) {
        // 如果找到匹配的文档，添加为新分段
        console.log(`找到匹配文档: ${matchedDocument.name} (ID: ${matchedDocument.id})`);

        // 将用例数据转换为分段格式
        const segments = processedData.map(row => {
          const contentText = `id: ${row.id};name: ${row.name};description: ${row.description};type: ${row.type};db_type: ${row.db_type};content: [${row.content.map(c => `'${c}'`).join(', ')}]`;
          // 预处理内容以确保作为单个分段
          const processedContent = preprocessUseCaseContent(contentText);
          return {
            content: processedContent,
            answer: row.description,
            keywords: [row.type, row.db_type, ...row.content.slice(0, 3)] // 取前3个内容作为关键词
          };
        });

        const response = await createDocumentSegments(knowledgeBase.id, matchedDocument.id, segments);

        if (response.success) {
          console.log('分段添加成功响应:', response);
          toast({
            title: "✅ 分段添加成功",
            description: `已向文档"${matchedDocument.name}"添加 ${segments.length} 个新分段（使用分段API）`,
          });

          // 重置表格
          setUseCaseRows([{
            id: '',
            name: '',
            description: '',
            type: '',
            db_type: '',
            content: ['']
          }]);
          setFileName(''); // 清空文件名
          await fetchDocuments(); // 刷新文档列表
        } else {
          toast({
            title: "添加分段失败",
            description: response.error || "添加分段时发生错误",
            variant: "destructive",
          });
        }
      } else {
        // 如果没有找到匹配的文档，按原来的方式创建新文件
        const finalFileName = fileName.trim()
          ? `${fileName.trim()}.csv`
          : `usecases_${new Date().toISOString().slice(0,19).replace(/[-T:]/g, '')}.csv`;

        // 转换为CSV
        const csvContent = jsonToCsv(processedData);
        const csvFile = downloadFile(csvContent, finalFileName, 'text/csv');

        // 上传文件
        if (knowledgeBase) {
          const response = await uploadUseCaseData(csvFile, knowledgeBase.id);

          if (response.success) {
            console.log('文件上传成功响应:', response);
            toast({
              title: "📄 文件上传成功",
              description: response.message || "用例数据已成功添加到知识库（使用文件上传API）",
            });

            // 重置表格
            setUseCaseRows([{
              id: '',
              name: '',
              description: '',
              type: '',
              db_type: '',
              content: ['']
            }]);
            setFileName(''); // 清空文件名
            await fetchDocuments(); // 刷新文档列表
          } else {
            toast({
              title: "上传失败",
              description: response.message || "上传过程中发生错误",
              variant: "destructive",
            });
          }
        }
      }
    } catch (error) {
      toast({
        title: "添加失败",
        description: error instanceof Error ? error.message : "请检查输入数据",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleDeleteUseCase = async () => {
    if (!deleteTarget.trim()) {
      toast({
        title: "删除失败",
        description: "请输入要删除的用例名称",
        variant: "destructive",
      });
      return;
    }

    try {
      // 这里应该调用删除API
      // 暂时模拟删除操作
      const filteredUseCases = useCases.filter(uc => uc.name !== deleteTarget);
      setUseCases(filteredUseCases);
      
      toast({
        title: "删除成功",
        description: `用例 "${deleteTarget}" 已被删除`,
      });
      
      setDeleteTarget('');
      setShowDeleteDialog(false);
    } catch (error) {
      toast({
        title: "删除失败",
        description: "删除过程中发生错误",
        variant: "destructive",
      });
    }
  };

  // 在 handleDeleteUseCase 函数之后添加这些函数
  const addNewRow = () => {
    setUseCaseRows([
      ...useCaseRows,
      {
        id: '',
        name: '',
        description: '',
        type: '',
        db_type: '',
        content: ['']
      }
    ]);
  };

  const removeRow = (index: number) => {
    if (useCaseRows.length > 1) {
      const newRows = [...useCaseRows];
      newRows.splice(index, 1);
      setUseCaseRows(newRows);
    }
  };

  const updateRow = (index: number, field: string, value: any) => {
    const newRows = [...useCaseRows];
    newRows[index] = { ...newRows[index], [field]: value };
    setUseCaseRows(newRows);
  };

  const updateContent = (index: number, contentIndex: number, value: string) => {
    const newRows = [...useCaseRows];
    const newContent = [...newRows[index].content];
    newContent[contentIndex] = value;
    newRows[index].content = newContent;
    setUseCaseRows(newRows);
  };

  const addContentField = (index: number) => {
    const newRows = [...useCaseRows];
    newRows[index].content.push('');
    setUseCaseRows(newRows);
  };

  const removeContentField = (rowIndex: number, contentIndex: number) => {
    if (useCaseRows[rowIndex].content.length > 1) {
      const newRows = [...useCaseRows];
      newRows[rowIndex].content.splice(contentIndex, 1);
      setUseCaseRows(newRows);
    }
  };

  const sampleJsonData = [
    {
      "id": "uc001",
      "name": "用户登录验证",
      "description": "用户登录系统的验证流程",
      "type": "身份验证",
      "db_type": "MySQL",
      "content": ["验证用户名和密码", "检查账户状态", "生成会话令牌"]
    },
    {
      "id": "uc002",
      "name": "数据导入处理",
      "description": "批量数据导入的处理流程",
      "type": "数据处理",
      "db_type": "PostgreSQL",
      "content": ["数据格式验证", "重复数据检测", "事务处理"]
    }
  ];

  if (!knowledgeBase) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          请先选择一个知识库。<a href="/" className="underline">返回知识库选择</a>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="w-6 h-6" />
            <span>用例经验知识库管理</span>
          </CardTitle>
          <CardDescription>
            当前知识库: {knowledgeBase.name} (ID: {knowledgeBase.id})
          </CardDescription>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <FileText className="w-5 h-5" />
                <span>用例文档列表</span>
              </div>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => fetchDocuments()}
                disabled={loadingDocuments}
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${loadingDocuments ? 'animate-spin' : ''}`} />
                刷新列表
              </Button>
            </CardTitle>
            <CardDescription>
              当前知识库中的所有文档，共 {documents.length} 个文档
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loadingDocuments ? (
              <div className="flex justify-center items-center h-40">
                <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : (
              <>
                {documents.length > 0 ? (
                  <>
                    <div className="border rounded-lg">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>文档ID</TableHead>
                            <TableHead>文档名称</TableHead>
                            <TableHead>类型</TableHead>
                            <TableHead>状态</TableHead>
                            <TableHead>创建时间</TableHead>
                            <TableHead>字数</TableHead>
                            <TableHead>操作</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {documents.map((doc) => (
                            <TableRow key={doc.id}>
                              <TableCell className="font-medium">
                                {doc.id}
                              </TableCell>
                              <TableCell className="font-medium">
                                {doc.name || '未命名文档'}
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline">{doc.type}</Badge>
                              </TableCell>
                              <TableCell>
                                {getStatusBadge(doc.status)}
                              </TableCell>
                              <TableCell className="text-sm">
                                {formatDate(doc.created_at)}
                              </TableCell>
                              <TableCell className="text-right">
                                {doc.word_count?.toLocaleString() || 0}
                              </TableCell>
                              <TableCell>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => {
                                    navigator.clipboard.writeText(doc.id);
                                    toast({
                                      title: "已复制",
                                      description: "文档ID已复制到剪贴板",
                                    });
                                  }}>
                                  复制ID
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                    {/* 分页组件 */}
                    <div className="flex items-center justify-between mt-4">
                      <div className="text-sm text-gray-500">
                        显示 {documents.length} 条中的第 {pagination.page} 页，共 {Math.ceil(pagination.total / pagination.limit)} 页
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(pagination.page - 1)}
                          disabled={pagination.page <= 1}
                        >
                          上一页
                        </Button>
                        <span className="text-sm">
                          {pagination.page}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(pagination.page + 1)}
                          disabled={pagination.page >= Math.ceil(pagination.total / pagination.limit)}
                        >
                          下一页
                        </Button>
                        <select 
                          value={pagination.limit}
                          onChange={(e) => handleLimitChange(Number(e.target.value))}
                          className="border rounded p-1 text-sm"
                        >
                          <option value={10}>10条/页</option>
                          <option value={20}>20条/页</option>
                          <option value={50}>50条/页</option>
                          <option value={100}>100条/页</option>
                        </select>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="flex flex-col items-center justify-center py-12 space-y-4 text-center">
                    <FileText className="w-16 h-16 text-gray-300" />
                    <h3 className="text-lg font-medium">暂无文档</h3>
                    <p className="text-sm text-gray-500">
                      当前知识库中还没有任何文档，请上传用例数据。
                    </p>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </CardHeader>
      </Card>

      {/* 功能选项卡 */}
      <Tabs defaultValue={defaultTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="search" className="flex items-center space-x-2">
            <Search className="w-4 h-4" />
            <span>查看用例</span>
          </TabsTrigger>
          <TabsTrigger value="add" className="flex items-center space-x-2">
            <Plus className="w-4 h-4" />
            <span>新增用例</span>
          </TabsTrigger>
          <TabsTrigger value="delete" className="flex items-center space-x-2">
            <Trash2 className="w-4 h-4" />
            <span>删除用例</span>
          </TabsTrigger>
        </TabsList>

        {/* 查看用例 */}
        <TabsContent value="search">
          <Card>
            <CardHeader>
              <CardTitle>搜索用例与文档内容</CardTitle>
              <CardDescription>
                输入关键词搜索用例经验数据和文档内容，或输入文档ID查看具体文档分段
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-end space-x-4">
                <div className="flex-1">
                  <Label htmlFor="search-input">搜索关键词或文档ID</Label>
                  <Input
                    id="search-input"
                    placeholder="输入关键词搜索所有文档，或输入文档ID查看具体内容..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  />
                  <p className="text-xs text-slate-500 mt-1">
                    提示：输入 UUID 格式的文档ID可查看该文档的所有分段内容
                  </p>
                </div>
                <Button onClick={handleSearch} disabled={loading || loadingSegments}>
                  {loading || loadingSegments ? '搜索中...' : '搜索'}
                </Button>
              </div>

              {/* 搜索模式提示 */}
              {searchMode && useCases.length > 0 && (
                <Alert className="mt-4">
                  <BookOpen className="h-4 w-4" />
                  <AlertDescription>
                    {searchMode === 'document' 
                      ? `正在显示文档 ${selectedDocumentId} 解析出的用例数据`
                      : `正在显示关键词 "${searchTerm}" 搜索到的用例数据`
                    }
                    {documentSegments.length > 0 && (
                      <div className="mt-2 space-x-2">
                        <Button
                          variant="link"
                          size="sm"
                          onClick={() => setShowRawSegments(!showRawSegments)}
                          className="p-0 h-auto"
                        >
                          {showRawSegments ? '隐藏原始分段' : '查看原始分段'}
                        </Button>
                      </div>
                    )}
                  </AlertDescription>
                </Alert>
              )}

              {/* 原始文档分段调试视图 */}
              {showRawSegments && documentSegments.length > 0 && (
                <div className="space-y-4 mt-4 p-4 bg-gray-50 rounded-lg border">
                  <div className="flex items-center justify-between">
                    <h4 className="text-md font-medium flex items-center space-x-2">
                      <Eye className="w-4 h-4" />
                      <span>原始文档分段（调试）</span>
                      <Badge variant="outline">{documentSegments.length} 个分段</Badge>
                    </h4>
                  </div>
                  
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {documentSegments.slice(0, 3).map((segment, index) => (
                      <div key={segment.id} className="p-3 bg-white rounded border text-xs">
                        <div className="font-mono text-gray-600 mb-2">
                          分段 {segment.position} (ID: {segment.id})
                        </div>
                        <div className="text-gray-800 whitespace-pre-wrap line-clamp-4">
                          {segment.content}
                        </div>
                      </div>
                    ))}
                    {documentSegments.length > 3 && (
                      <div className="text-center text-gray-500 text-sm">
                        还有 {documentSegments.length - 3} 个分段...
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* 传统用例搜索结果表格 */}
              {useCases.length > 0 && (
                <div className="space-y-6">
                  {documentSegments.length > 0 ? (
                    /* 显示分段内容 */
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <h3 className="text-lg font-medium flex items-center space-x-2">
                          <BookOpen className="w-5 h-5" />
                          <span>文档内容</span>
                          <Badge variant="secondary">{documentSegments.length} 个分段</Badge>
                        </h3>
                        
                        <div className="border rounded-lg">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>文档ID</TableHead>
                                {/* <TableHead>ID</TableHead> */}
                                <TableHead>名称</TableHead>
                                <TableHead>描述</TableHead>
                                {/* <TableHead>类型</TableHead> */}
                                <TableHead>数据库类型</TableHead>
                                <TableHead>内容</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {/* 解析所有分段的内容为用例 */}
                              {parseSegmentToUseCases(documentSegments).map((useCase, index) => (
                                <TableRow key={`${useCase.document_id}-${index}`}>
                                  <TableCell className="font-mono text-sm">
                                    {useCase.document_id || '未知'}
                                  </TableCell>
                                  {/* <TableCell className="font-mono text-sm">
                                    {useCase.id}
                                  </TableCell> */}
                                  <TableCell>
                                    {useCase.name}
                                  </TableCell>
                                  <TableCell>
                                    {useCase.description}
                                  </TableCell>
                                  {/* <TableCell>
                                    <Badge variant="outline">{useCase.type}</Badge>
                                  </TableCell> */}
                                  <TableCell>
                                    <Badge variant="secondary">{useCase.db_type}</Badge>
                                  </TableCell>
                                  <TableCell className="max-w-xs">
                                    <div className="space-y-1">
                                      {Array.isArray(useCase.content) ? 
                                        useCase.content.map((item, contentIndex) => (
                                          <div key={contentIndex} className="text-sm text-slate-600 truncate">
                                            • {item}
                                          </div>
                                        )) :
                                        <div className="text-sm text-slate-600 truncate">
                                          {useCase.content}
                                        </div>
                                      }
                                    </div>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      </div>
                    </div>
                  ) : (
                    /* 如果没有分段数据，显示普通用例表格 */
                    <div className="border rounded-lg">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>ID</TableHead>
                            <TableHead>名称</TableHead>
                            <TableHead>描述</TableHead>
                            <TableHead>类型</TableHead>
                            <TableHead>数据库类型</TableHead>
                            <TableHead>内容</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {useCases.map((useCase) => (
                            <TableRow key={useCase.id}>
                              <TableCell className="font-mono text-sm">
                                {useCase.id}
                              </TableCell>
                              <TableCell className="font-medium">
                                {useCase.name}
                              </TableCell>
                              <TableCell className="max-w-xs">
                                <div className="truncate" title={useCase.description}>
                                  {useCase.description}
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline">{useCase.type}</Badge>
                              </TableCell>
                              <TableCell>
                                <Badge variant="secondary">{useCase.db_type}</Badge>
                              </TableCell>
                              <TableCell className="max-w-xs">
                                <div className="space-y-1">
                                  {Array.isArray(useCase.content) ? 
                                    useCase.content.map((item, index) => (
                                      <div key={index} className="text-sm text-slate-600 truncate">
                                        • {item}
                                      </div>
                                    )) :
                                    <div className="text-sm text-slate-600 truncate">
                                      {useCase.content}
                                    </div>
                                  }
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </div>
              )}

              {/* 空状态提示 */}
              {useCases.length === 0 && !loading && !loadingSegments && searchTerm && (
                <div className="text-center py-8 text-slate-500">
                  <FileText className="w-12 h-12 mx-auto mb-4 text-slate-300" />
                  <p>暂无搜索结果，请尝试其他关键词或文档ID</p>
                  <p className="text-xs mt-2">
                    提示：可以从文档列表中复制文档ID进行精确查询
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 新增用例 */}
        <TabsContent value="add">
          <Card>
            <CardHeader>
              <CardTitle>新增用例数据</CardTitle>
              <CardDescription>
                填写以下表单添加新的用例数据
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 成功提示 - 添加在顶部 */}
              {submitSuccess && (
                <Alert className="mb-4">
                  <CheckCircle2 className="h-4 w-4" />
                  <AlertDescription>
                    <strong>提交成功!</strong> 用例数据已成功添加到知识库
                  </AlertDescription>
                </Alert>
              )}
             
              <div>
                <Label htmlFor="file-name">用例数据文件名（可选）</Label>
                <Input
                  id="file-name"
                  placeholder="输入文件名（不包括扩展名）"
                  value={fileName}
                  onChange={(e) => setFileName(e.target.value)}
                />
          		<p className="text-blue-600 text-xs mt-1">
            		💡 <span className="font-medium">智能添加模式：</span>如果输入的文件名与现有文档匹配，将作为新分段添加到该文档中
          		</p>
                <p className="text-xs text-slate-500 mt-1">
                  {fileName.trim()
                    ? `${findDocumentByName(fileName.trim()) ? '🎯 匹配到现有文档，将添加为新分段' : '📄 将创建新文件: ' + fileName.trim() + '.csv'}`
                    : '不填写将自动生成文件名 (如: usecases_202506241230.csv)'}
                </p>
              </div>
 
              {/* 用例列表 */}
              <div className="space-y-4">
                {useCaseRows.map((row, index) => (
                  <Card key={index} className="border rounded-lg p-4 bg-white shadow-sm">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-medium">用例 #{index + 1}</h3>
                      <Button
                        variant="destructive"
                        size="icon"
                        onClick={() => removeRow(index)}
                        disabled={useCaseRows.length <= 1}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* 左侧基础信息 */}
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor={`id-${index}`}>用例ID *</Label>
                          <Input
                            id={`id-${index}`}
                            placeholder="唯一标识符"
                            value={row.id}
                            onChange={(e) => updateRow(index, 'id', e.target.value)}
                            className={validationErrors[`id-${index}`] ? "border-red-500" : ""}
                          />
                          {/* 验证错误提示 - 添加在输入框下方 */}
                          {validationErrors[`id-${index}`] && (
                            <p className="text-red-500 text-xs mt-1">{validationErrors[`id-${index}`]}</p>
                          )}
                        </div>
                        
                        <div>
                          <Label htmlFor={`name-${index}`}>用例名称 *</Label>
                          <Input
                            id={`name-${index}`}
                            placeholder="用例名称"
                            value={row.name}
                            onChange={(e) => updateRow(index, 'name', e.target.value)}
                            className={validationErrors[`name-${index}`] ? "border-red-500" : ""}
                          />
                          {validationErrors[`name-${index}`] && (
                            <p className="text-red-500 text-xs mt-1">{validationErrors[`name-${index}`]}</p>
                          )}
                        </div>
                        
                        <div>
                          <Label htmlFor={`description-${index}`}>用例描述 *</Label>
                          <Textarea
                            id={`description-${index}`}
                            placeholder="用例描述"
                            value={row.description}
                            onChange={(e) => updateRow(index, 'description', e.target.value)}
                            rows={3}
                            className={validationErrors[`description-${index}`] ? "border-red-500" : ""}
                          />
                          {validationErrors[`description-${index}`] && (
                            <p className="text-red-500 text-xs mt-1">{validationErrors[`description-${index}`]}</p>
                          )}
                        </div>
                      </div>
                      
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor={`type-${index}`}>用例类型 *</Label>
                          <Input
                            id={`type-${index}`}
                            placeholder="用例类型"
                            value={row.type}
                            onChange={(e) => updateRow(index, 'type', e.target.value)}
                            className={validationErrors[`type-${index}`] ? "border-red-500" : ""}
                          />
                          {validationErrors[`type-${index}`] && (
                            <p className="text-red-500 text-xs mt-1">{validationErrors[`type-${index}`]}</p>
                          )}
                        </div>

                        <div>
                          <Label htmlFor={`db_type-${index}`}>数据库类型 *</Label>
                          <select
                            id={`db_type-${index}`}
                            value={row.db_type}
                            onChange={(e) => updateRow(index, 'db_type', e.target.value)}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                          >
                            <option value="">请选择数据库类型</option>
                            {dbTypes.map((dbType) => (
                              <option key={dbType} value={dbType}>
                                {dbType}
                              </option>
                            ))}
                          </select>
                          {validationErrors[`db_type-${index}`] && (
                            <p className="text-red-500 text-xs mt-1">{validationErrors[`db_type-${index}`]}</p>
                          )}
                        </div>
                        
                        <div>
                          <Label>用例列表 *</Label>
                          <div className="space-y-2">
                            {row.content.map((contentItem, contentIndex) => (
                              <div key={contentIndex} className="flex items-center space-x-2">
                                <Input
                                  placeholder={`用例 ${contentIndex + 1}`}
                                  value={contentItem}
                                  onChange={(e) => updateContent(index, contentIndex, e.target.value)}
                                />
                                <Button
                                  variant="destructive"
                                  size="icon"
                                  onClick={() => removeContentField(index, contentIndex)}
                                  disabled={row.content.length <= 1}
                                >
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </div>
                            ))}
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => addContentField(index)}
                              className="mt-1"
                            >
                              <Plus className="w-4 h-4 mr-1" /> 添加用例
                            </Button>
                          </div>
                          {validationErrors[`content-${index}`] && (
                            <p className="text-red-500 text-xs mt-1">{validationErrors[`content-${index}`]}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>

              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <Button
                  variant="outline"
                  onClick={addNewRow}
                  className="flex items-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>添加新用例</span>
                </Button>
                
                <Button
                  onClick={handleAddUseCases}
                  disabled={isUploading}
                  style={{
                    backgroundColor: 'white', // 深蓝色
                    color: 'black',
                    // fontWeight: 'bold',
                    // fontSize: '1rem',
                    // padding: '0.75rem 1.5rem',
                    // borderRadius: '0.5rem',
                    // boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                    // border: '2px solidrgb(12, 12, 13)',
                    // textShadow: '0 1px 1px rgba(0, 0, 0, 0.25)'
                  }}
                  className="flex items-center space-x-2"
                >
                  {isUploading ? (
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span style={{ fontWeight: 'bold' }}>上传中...</span>
                    </div>
                  ) : (
                    <>
                      <Upload className="w-5 h-5" />
                      <span>提交所有用例</span>
                    </>
                  )}
                </Button>

              </div>

              {/* 格式说明 */}
              <Alert className="mt-6">
                <CheckCircle2 className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <p><strong>填写说明：</strong></p>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      <li>所有带 <span className="text-red-500">*</span> 的字段为必填项</li>
                      <li>用例ID: 暂时没用到，可以写1,2,3..即可</li>
                      <li>用例名称: 对应智研的测试设计的测试点(‼️)</li>
                      <li>用例描述: 对应智研的用例描述(‼️)</li>
                      <li>用例类型: 暂时没用到,可以填写dml、ddl、table等</li>
                      <li>数据库类型: 根据下拉框填写对应产品即可</li>
                      <li>用例列表: 可以理解为对应智研的用例标题,一个测试点对应多个测试用例</li>
                    </ul>
                  </div>
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 删除用例 */}
        <TabsContent value="delete">
          <Card>
            <CardHeader>
              <CardTitle>删除用例</CardTitle>
              <CardDescription>
                输入文档ID删除整个文档，或输入关键词删除匹配的分段
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="delete-input">文档ID或关键词</Label>
                <Input
                  id="delete-input"
                  placeholder="输入文档ID删除整个文档，或输入关键词删除匹配的分段..."
                  value={deleteTarget}
                  onChange={(e) => setDeleteTarget(e.target.value)}
                />
                <p className="text-xs text-slate-500 mt-1">
                  提示：输入 UUID 格式的文档ID可删除整个文档，输入关键词可删除所有匹配的分段
                </p>
              </div>

              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={!deleteTarget.trim() || loading}
                className="flex items-center space-x-2"
              >
                <Trash2 className="w-4 h-4" />
                <span>{loading ? '删除中...' : '删除'}</span>
              </Button>

              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>注意：</strong>删除操作将永久移除用例数据，请谨慎操作。
                  <br />
                  • 输入文档ID：删除整个文档及其所有分段
                  <br />
                  • 输入关键词：删除所有包含该关键词的分段
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
