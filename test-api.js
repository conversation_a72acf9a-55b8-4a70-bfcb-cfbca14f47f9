// 测试外部API的行为
const axios = require('axios');

async function testSegmentAPI() {
  const config = {
    apiKey: process.env.API_KEY || 'your-api-key',
    apiIp: process.env.API_IP || '*************',
  };

  const knowledgeBaseId = 'test-kb-id';
  const documentId = 'test-doc-id';

  const headers = {
    'Authorization': `Bearer ${config.apiKey}`,
    'Content-Type': 'application/json',
  };

  const url = `http://${config.apiIp}/v1/datasets/${knowledgeBaseId}/documents/${documentId}/segments`;

  // 第一次添加分段
  const firstSegments = {
    segments: [
      {
        content: "第一个分段内容",
        answer: "第一个答案",
        keywords: ["测试", "第一"]
      }
    ]
  };

  console.log('第一次添加分段...');
  try {
    const response1 = await axios.post(url, firstSegments, { headers });
    console.log('第一次添加成功:', response1.status, response1.data);
  } catch (error) {
    console.error('第一次添加失败:', error.response?.status, error.response?.data || error.message);
  }

  // 等待一秒
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 第二次添加分段
  const secondSegments = {
    segments: [
      {
        content: "第二个分段内容",
        answer: "第二个答案", 
        keywords: ["测试", "第二"]
      }
    ]
  };

  console.log('第二次添加分段...');
  try {
    const response2 = await axios.post(url, secondSegments, { headers });
    console.log('第二次添加成功:', response2.status, response2.data);
  } catch (error) {
    console.error('第二次添加失败:', error.response?.status, error.response?.data || error.message);
  }

  // 获取文档分段查看结果
  console.log('获取文档分段...');
  try {
    const getResponse = await axios.get(`${url}?page=1&limit=10&enabled=all`, { headers });
    console.log('获取分段成功:', getResponse.status);
    console.log('分段数量:', getResponse.data.data?.length || 0);
    console.log('分段内容:', getResponse.data.data?.map(seg => seg.content) || []);
  } catch (error) {
    console.error('获取分段失败:', error.response?.status, error.response?.data || error.message);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testSegmentAPI().catch(console.error);
}

module.exports = { testSegmentAPI };
