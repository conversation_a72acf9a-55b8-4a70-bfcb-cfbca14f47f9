# SQL自动分段问题修复

## 问题描述
用户报告：添加SQL时，多行的SQL数据被拆分成了2个分段，按理应该是一个分段。

### 示例问题内容
```
TDSQL-PG:复杂聚合查询,窗口函数与分组
:ident:
```sql
select 
  id,
  name,
  row_number() over (partition by dept_id order by salary desc) as rank
from employees 
where status = 'active';

select 
  dept_id,
  avg(salary) as avg_salary,
  count(*) as emp_count
from employees 
group by dept_id 
having count(*) > 5;
```
```

## 问题分析
经过分析，问题可能出现在外部API的自动分段逻辑上。可能的分段触发因素：

1. **`:ident:` 标记** - 这个特殊标记可能被识别为分段分隔符
2. **代码块标记** - `sql` 和 ``` 标记可能导致分段
3. **空行** - 多个空行可能被视为分段分隔符
4. **多个SQL语句** - 两个独立的SELECT语句可能被自动分段

## 解决方案

### 方案1：内容预处理
在发送到外部API之前对SQL内容进行预处理：

```typescript
const preprocessSqlContent = (content: string): string => {
  let processed = content;
  
  // 移除可能导致分段的特殊标记
  processed = processed.replace(/:ident:/g, '');
  processed = processed.replace(/```sql\s*/g, '');
  processed = processed.replace(/```\s*/g, '');
  
  // 将多个连续空行替换为单个空行
  processed = processed.replace(/\n\s*\n\s*\n/g, '\n\n');
  
  // 将整个内容包装在JSON结构中
  const wrappedContent = {
    type: "sql_content",
    title: "SQL查询语句",
    content: processed.trim(),
    timestamp: new Date().toISOString()
  };
  
  return `SQL内容块:\n${JSON.stringify(wrappedContent, null, 2)}`;
};
```

### 方案2：明确的分段标识
为内容添加明确的开始和结束标识：

```typescript
const processedContent = `-- SQL内容开始\n${content}\n-- SQL内容结束`;
```

## 实施的修改

### 文件：`src/components/SqlManagement.tsx`

1. **添加预处理函数**：
   - 移除 `:ident:` 标记
   - 移除代码块标记
   - 规范化空行
   - 将内容包装在JSON结构中

2. **修改分段创建逻辑**：
   ```typescript
   // 将SQL内容转换为分段格式
   const processedContent = preprocessSqlContent(textInput.trim());
   
   const segments = [{
     content: processedContent,
     answer: "SQL语句",
     keywords: ["SQL", "数据库", "查询"]
   }];
   ```

### 文件：`src/components/UseCaseManagement.tsx`

为用例内容也添加了类似的预处理逻辑，以防止类似问题。

## 测试验证

### 测试步骤
1. **准备测试内容**：使用包含多个SQL语句、空行和特殊标记的内容
2. **第一次测试**：创建新文档，添加复杂SQL内容
3. **验证结果**：检查是否只创建了一个分段
4. **第二次测试**：向现有文档添加类似内容
5. **验证结果**：检查是否正确追加为单个分段

### 预期结果
- ✅ 复杂的多行SQL内容被识别为单个分段
- ✅ 不会因为空行或特殊标记而被拆分
- ✅ 内容完整性得到保持

## 备选方案

如果当前方案不能完全解决问题，可以考虑：

### 方案A：使用不同的API参数
检查外部API是否有参数可以控制自动分段行为。

### 方案B：内容转义
将整个内容进行Base64编码或其他编码，避免被解析：

```typescript
const encodedContent = btoa(unescape(encodeURIComponent(content)));
const wrappedContent = `ENCODED_SQL_CONTENT:${encodedContent}`;
```

### 方案C：使用单行格式
将多行SQL压缩为单行，避免换行符导致的分段：

```typescript
const singleLineContent = content.replace(/\s+/g, ' ').trim();
```

## 监控和调试

添加了详细的日志输出来跟踪预处理过程：

```typescript
console.log('SQL内容预处理:', {
  original: content.substring(0, 100) + '...',
  processed: finalContent.substring(0, 100) + '...',
  originalLength: content.length,
  processedLength: finalContent.length
});
```

这些日志可以帮助我们了解：
- 预处理是否正确执行
- 内容长度变化
- 处理前后的内容差异

## 后续优化

1. **用户反馈收集**：收集用户对新格式的反馈
2. **性能监控**：监控预处理对性能的影响
3. **格式优化**：根据实际使用情况优化包装格式
4. **API研究**：深入研究外部API的分段规则，寻找更优解决方案

这个修复应该能够解决SQL内容被意外分段的问题，确保完整的SQL块被正确识别为单个分段。
